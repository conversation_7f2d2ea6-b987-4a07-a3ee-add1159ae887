//
//  DocumentWebView.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/20.
//

import SafariServices
import SwiftUI
@preconcurrency import WebKit

/// WebView component for displaying legal documents with Markdown support
struct DocumentWebView: UIViewRepresentable {
  let documentType: DocumentType
  @Binding var isLoading: Bool
  @Binding var error: DocumentError?

  func makeUIView(context: Context) -> WKWebView {
    let configuration = WKWebViewConfiguration()
    configuration.allowsInlineMediaPlayback = true
    configuration.mediaTypesRequiringUserActionForPlayback = []

    let webView = WKWebView(frame: .zero, configuration: configuration)
    webView.navigationDelegate = context.coordinator
    webView.scrollView.backgroundColor = UIColor(red: 0.035, green: 0.035, blue: 0.043, alpha: 1.0)  // #09090b
    webView.backgroundColor = UIColor(red: 0.035, green: 0.035, blue: 0.043, alpha: 1.0)
    webView.isOpaque = false

    return webView
  }

  func updateUIView(_ webView: WKWebView, context: Context) {
    guard !isLoading else { return }

    loadDocument(in: webView)
  }

  func makeCoordinator() -> Coordinator {
    Coordinator(self)
  }

  // MARK: - Private Methods

  private func loadDocument(in webView: WKWebView) {
    guard let htmlContent = loadHTMLContent() else {
      error = .fileNotFound
      return
    }

    let fullHTML = generateHTML(from: htmlContent)
    webView.loadHTMLString(fullHTML, baseURL: Bundle.main.bundleURL)
  }

  private func loadHTMLContent() -> String? {
    let fileName = documentType.fileName
    guard let path = Bundle.main.path(forResource: fileName, ofType: "html"),
      let content = try? String(contentsOfFile: path)
    else {
      return nil
    }
    return content
  }

  private func generateHTML(from htmlContent: String) -> String {
    let css = generateCSS()

    return """
      <!DOCTYPE html>
      <html>
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>\(documentType.title)</title>
          <style>\(css)</style>
      </head>
      <body>
          \(htmlContent)
          <script>
              // Handle external links
              document.addEventListener('click', function(e) {
                  if (e.target.tagName === 'A' && e.target.href.startsWith('http')) {
                      e.preventDefault();
                      window.webkit.messageHandlers.linkHandler.postMessage(e.target.href);
                  }
              });
          </script>
      </body>
      </html>
      """
  }

  private func generateCSS() -> String {
    return """
      body {
          font-family: -apple-system, BlinkMacSystemFont, 'Inter', 'Segoe UI', Roboto, sans-serif;
          line-height: 1.6;
          color: #ffffff;
          background-color: #09090b;
          margin: 0;
          padding: 20px;
          -webkit-text-size-adjust: 100%;
      }

      h1, h2, h3, h4, h5, h6 {
          color: #ffffff;
          margin-top: 24px;
          margin-bottom: 16px;
          font-weight: 600;
      }

      h1 {
          font-size: 28px;
          border-bottom: 1px solid #27272a;
          padding-bottom: 12px;
      }

      h2 {
          font-size: 24px;
          margin-top: 32px;
      }

      h3 {
          font-size: 20px;
          margin-top: 24px;
      }

      p {
          margin-bottom: 16px;
          color: #e4e4e7;
      }

      ul, ol {
          margin-bottom: 16px;
          padding-left: 24px;
      }

      li {
          margin-bottom: 8px;
          color: #e4e4e7;
      }

      a {
          color: #3b82f6;
          text-decoration: none;
      }

      a:hover {
          text-decoration: underline;
      }

      strong {
          font-weight: 600;
          color: #ffffff;
      }

      em {
          font-style: italic;
      }

      code {
          background-color: #27272a;
          color: #e4e4e7;
          padding: 2px 6px;
          border-radius: 4px;
          font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
          font-size: 14px;
      }

      pre {
          background-color: #27272a;
          color: #e4e4e7;
          padding: 16px;
          border-radius: 8px;
          overflow-x: auto;
          margin-bottom: 16px;
      }

      blockquote {
          border-left: 4px solid #3b82f6;
          margin: 16px 0;
          padding-left: 16px;
          color: #a1a1aa;
          font-style: italic;
      }

      hr {
          border: none;
          border-top: 1px solid #27272a;
          margin: 32px 0;
      }

      table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 16px;
      }

      th, td {
          border: 1px solid #27272a;
          padding: 12px;
          text-align: left;
      }

      th {
          background-color: #27272a;
          font-weight: 600;
          color: #ffffff;
      }

      td {
          color: #e4e4e7;
      }

      /* Responsive design */
      @media (max-width: 768px) {
          body {
              padding: 16px;
          }
          
          h1 {
              font-size: 24px;
          }
          
          h2 {
              font-size: 20px;
          }
          
          h3 {
              font-size: 18px;
          }
      }
      """
  }
}

// MARK: - Coordinator

extension DocumentWebView {
  class Coordinator: NSObject, WKNavigationDelegate {
    var parent: DocumentWebView
    private var loadingTimer: Timer?

    init(_ parent: DocumentWebView) {
      self.parent = parent
      super.init()
    }

    func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
      // Only show loading indicator if loading takes longer than 200ms
      // This prevents flash of loading for local content
      loadingTimer = Timer.scheduledTimer(withTimeInterval: 0.2, repeats: false) { _ in
        DispatchQueue.main.async {
          self.parent.isLoading = true
        }
      }
    }

    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
      loadingTimer?.invalidate()
      loadingTimer = nil
      parent.isLoading = false
    }

    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
      loadingTimer?.invalidate()
      loadingTimer = nil
      parent.isLoading = false
      parent.error = .loadingFailed(error.localizedDescription)
    }

    func webView(
      _ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction,
      decisionHandler: @escaping (WKNavigationActionPolicy) -> Void
    ) {
      // Handle external links
      if let url = navigationAction.request.url,
        url.scheme == "http" || url.scheme == "https",
        navigationAction.navigationType == .linkActivated
      {

        // Open external links in Safari
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
          let rootViewController = windowScene.windows.first?.rootViewController
        {
          let safariVC = SFSafariViewController(url: url)
          safariVC.preferredBarTintColor = UIColor(
            red: 0.035, green: 0.035, blue: 0.043, alpha: 1.0)
          safariVC.preferredControlTintColor = UIColor.white
          rootViewController.present(safariVC, animated: true)
        }

        decisionHandler(.cancel)
        return
      }

      decisionHandler(.allow)
    }
  }
}

// MARK: - Supporting Types

enum DocumentType {
  case termsOfService
  case privacyPolicy

  var title: String {
    switch self {
    case .termsOfService:
      return "Terms of Service"
    case .privacyPolicy:
      return "Privacy Policy"
    }
  }

  var fileName: String {
    switch self {
    case .termsOfService:
      return "picadabra-ai-tos"
    case .privacyPolicy:
      return "picadabra-ai-privacy-policy"
    }
  }
}

enum DocumentError: Error, LocalizedError {
  case fileNotFound
  case loadingFailed(String)

  var errorDescription: String? {
    switch self {
    case .fileNotFound:
      return "Document file not found"
    case .loadingFailed(let message):
      return "Failed to load document: \(message)"
    }
  }
}
